import React, {useEffect} from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {EditorRootState} from '../../store/EditorRootState';
import {fetchOrganizationCredits} from '../../actions/pricingActions';

interface CreditDisplayProps {
  onPress?: () => void;
  showDetails?: boolean;
}

export const CreditDisplay: React.FC<CreditDisplayProps> = ({onPress, showDetails = false}) => {
  const dispatch = useDispatch();
  const orgId = useSelector((state: EditorRootState) => state.platform.orgId);
  const pricingState = useSelector((state: EditorRootState) => state.pricing);
  const {organizationCredits, creditsLoading} = pricingState;

  useEffect(() => {
    if (orgId && !organizationCredits && !creditsLoading) {
      dispatch(fetchOrganizationCredits({organizationId: orgId}));
    }
  }, [orgId, organizationCredits, creditsLoading, dispatch]);

  // Mock data for demo purposes
  const mockCredits = {
    balance: 85,
    used: 15,
    total: 100,
  };

  const credits = organizationCredits || mockCredits;
  const percentage = credits.total > 0 ? (credits.balance / credits.total) * 100 : 0;

  const getStatusColor = () => {
    if (percentage > 50) return '#4CAF50'; // Green
    if (percentage > 20) return '#FF9800'; // Orange
    return '#F44336'; // Red
  };

  const getStatusText = () => {
    if (percentage > 50) return 'Good';
    if (percentage > 20) return 'Low';
    return 'Critical';
  };

  if (creditsLoading) {
    return (
      <View style={styles.container}>
        <Icon name="loading" size={16} color="#999" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <Pressable 
      style={[styles.container, onPress && styles.pressable]} 
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.iconContainer}>
        <Icon name="coin" size={16} color="#FFD700" />
      </View>
      
      <View style={styles.creditInfo}>
        <Text style={styles.creditText}>
          {credits.balance} credits
        </Text>
        
        {showDetails && (
          <View style={styles.detailsContainer}>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  {width: `${percentage}%`, backgroundColor: getStatusColor()}
                ]} 
              />
            </View>
            <Text style={[styles.statusText, {color: getStatusColor()}]}>
              {getStatusText()}
            </Text>
            <Text style={styles.usageText}>
              {credits.used} used of {credits.total}
            </Text>
          </View>
        )}
      </View>

      {onPress && (
        <Icon name="chevron-right" size={16} color="#666" />
      )}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#2a2a2a',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#3a3a3a',
  },
  pressable: {
    cursor: 'pointer',
  },
  iconContainer: {
    marginRight: 8,
  },
  creditInfo: {
    flex: 1,
  },
  creditText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  loadingText: {
    color: '#999',
    fontSize: 14,
    marginLeft: 8,
  },
  detailsContainer: {
    marginTop: 4,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#4a4a4a',
    borderRadius: 2,
    marginTop: 4,
    marginBottom: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 2,
  },
  usageText: {
    fontSize: 11,
    color: '#999',
  },
});
