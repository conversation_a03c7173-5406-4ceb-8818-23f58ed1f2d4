import React, {useEffect, useRef} from 'react';
import {View, StyleSheet} from 'react-native';

interface PaddleCheckoutProps {
  checkoutData: {
    checkoutId: string;
    checkoutUrl: string;
  } | null;
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
}

declare global {
  interface Window {
    Paddle: any;
  }
}

export const PaddleCheckout: React.FC<PaddleCheckoutProps> = ({
  checkoutData,
  onSuccess,
  onError,
  onClose,
}) => {
  const paddleInitialized = useRef(false);

  useEffect(() => {
    // Load Paddle.js script
    if (!paddleInitialized.current) {
      const script = document.createElement('script');
      script.src = 'https://cdn.paddle.com/paddle/paddle.js';
      script.async = true;
      script.onload = () => {
        if (window.Paddle) {
          // Initialize Paddle with your client-side token
          window.Paddle.Setup({
            vendor: 'test_f0ff365cf3b22228fc6dde90827', // Your Paddle client token
            eventCallback: (data: any) => {
              console.log('Paddle event:', data);
              
              switch (data.event) {
                case 'Checkout.Complete':
                  onSuccess?.(data);
                  break;
                case 'Checkout.Close':
                  onClose?.();
                  break;
                case 'Checkout.Error':
                  onError?.(data);
                  break;
                default:
                  break;
              }
            },
          });
          paddleInitialized.current = true;
        }
      };
      script.onerror = () => {
        console.error('Failed to load Paddle.js');
        onError?.({message: 'Failed to load Paddle checkout'});
      };
      document.head.appendChild(script);

      return () => {
        // Cleanup script on unmount
        document.head.removeChild(script);
      };
    }
  }, [onSuccess, onError, onClose]);

  useEffect(() => {
    if (checkoutData && window.Paddle && paddleInitialized.current) {
      // Open Paddle checkout
      window.Paddle.Checkout.open({
        override: checkoutData.checkoutUrl,
        successCallback: (data: any) => {
          console.log('Paddle checkout success:', data);
          onSuccess?.(data);
        },
        closeCallback: () => {
          console.log('Paddle checkout closed');
          onClose?.();
        },
      });
    }
  }, [checkoutData, onSuccess, onClose]);

  return <View style={styles.container} />;
};

const styles = StyleSheet.create({
  container: {
    display: 'none', // Hidden component, Paddle handles the UI
  },
});

// Utility function to open Paddle checkout programmatically
export const openPaddleCheckout = (
  checkoutUrl: string,
  options: {
    onSuccess?: (data: any) => void;
    onError?: (error: any) => void;
    onClose?: () => void;
  } = {}
) => {
  if (window.Paddle) {
    window.Paddle.Checkout.open({
      override: checkoutUrl,
      successCallback: options.onSuccess,
      closeCallback: options.onClose,
    });
  } else {
    // Fallback to opening in new window
    window.open(checkoutUrl, '_blank');
  }
};

// Hook for using Paddle checkout
export const usePaddleCheckout = () => {
  const [isReady, setIsReady] = React.useState(false);

  useEffect(() => {
    if (window.Paddle) {
      setIsReady(true);
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://cdn.paddle.com/paddle/paddle.js';
    script.async = true;
    script.onload = () => {
      if (window.Paddle) {
        window.Paddle.Setup({
          vendor: 'test_f0ff365cf3b22228fc6dde90827',
        });
        setIsReady(true);
      }
    };
    document.head.appendChild(script);

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  const openCheckout = React.useCallback((
    checkoutUrl: string,
    callbacks: {
      onSuccess?: (data: any) => void;
      onError?: (error: any) => void;
      onClose?: () => void;
    } = {}
  ) => {
    if (isReady && window.Paddle) {
      window.Paddle.Checkout.open({
        override: checkoutUrl,
        successCallback: callbacks.onSuccess,
        closeCallback: callbacks.onClose,
      });
    } else {
      // Fallback
      window.open(checkoutUrl, '_blank');
    }
  }, [isReady]);

  return {
    isReady,
    openCheckout,
  };
};
