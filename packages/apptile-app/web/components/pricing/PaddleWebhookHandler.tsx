import React, {useEffect} from 'react';
import {useDispatch} from 'react-redux';
import {updateOrganizationCredits, fetchOrganizationCredits} from '../../actions/pricingActions';
import {makeToast} from '../../actions/toastActions';

interface PaddleWebhookHandlerProps {
  organizationId?: string;
}

// This component handles Paddle webhook events
// In a real implementation, this would be handled by your backend
export const PaddleWebhookHandler: React.FC<PaddleWebhookHandlerProps> = ({organizationId}) => {
  const dispatch = useDispatch();

  useEffect(() => {
    // Listen for Paddle checkout completion events
    const handlePaddleEvent = (event: MessageEvent) => {
      if (event.origin !== 'https://checkout.paddle.com') {
        return; // Only accept messages from Paddle
      }

      const data = event.data;
      
      if (data.event === 'Checkout.Complete') {
        handleCheckoutComplete(data);
      } else if (data.event === 'Checkout.Close') {
        handleCheckoutClose(data);
      }
    };

    // Add event listener for Paddle events
    window.addEventListener('message', handlePaddleEvent);

    return () => {
      window.removeEventListener('message', handlePaddleEvent);
    };
  }, [organizationId, dispatch]);

  const handleCheckoutComplete = (data: any) => {
    console.log('Paddle checkout completed:', data);
    
    if (!organizationId) {
      console.error('No organization ID provided for credit allocation');
      return;
    }

    // Extract plan information from checkout data
    const planId = data.checkout?.custom_data?.plan_id;
    const billingInterval = data.checkout?.custom_data?.billing_interval;
    
    // Determine credits to allocate based on plan
    let creditsToAllocate = 0;
    
    switch (planId) {
      case 'free':
        creditsToAllocate = 5;
        break;
      case 'pro':
        // For Pro plan, credits depend on the selected tier
        const selectedCredits = data.checkout?.custom_data?.selected_credits || 100;
        creditsToAllocate = selectedCredits;
        break;
      case 'teams':
        creditsToAllocate = 100;
        break;
      default:
        creditsToAllocate = 0;
    }

    if (creditsToAllocate > 0) {
      // Dispatch action to update organization credits
      dispatch(updateOrganizationCredits({
        organizationId,
        credits: creditsToAllocate,
        source: 'paddle_purchase',
      }));

      // Show success message
      dispatch(makeToast({
        content: `Successfully added ${creditsToAllocate} credits to your account!`,
        appearances: 'success',
      }));

      // Refresh credits display
      setTimeout(() => {
        dispatch(fetchOrganizationCredits({organizationId}));
      }, 1000);
    }
  };

  const handleCheckoutClose = (data: any) => {
    console.log('Paddle checkout closed:', data);
    
    // You might want to handle checkout abandonment here
    // For example, send analytics events or show a retention message
  };

  // This component doesn't render anything visible
  return null;
};

// Utility function to simulate webhook processing for demo purposes
export const simulateWebhookProcessing = (
  planId: string,
  billingInterval: 'monthly' | 'annual',
  organizationId: string,
  selectedCredits?: number
) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let creditsToAllocate = 0;
      
      switch (planId) {
        case 'free':
          creditsToAllocate = 5;
          break;
        case 'pro':
          creditsToAllocate = selectedCredits || 100;
          break;
        case 'teams':
          creditsToAllocate = 100;
          break;
        default:
          creditsToAllocate = 0;
      }

      resolve({
        success: true,
        creditsAllocated: creditsToAllocate,
        organizationId,
        planId,
        billingInterval,
      });
    }, 2000); // Simulate processing delay
  });
};

// Hook for handling Paddle events
export const usePaddleWebhooks = (organizationId?: string) => {
  const dispatch = useDispatch();

  const processWebhook = React.useCallback(async (webhookData: any) => {
    try {
      // In a real implementation, this would call your backend API
      // For demo purposes, we'll simulate the processing
      const result = await simulateWebhookProcessing(
        webhookData.planId,
        webhookData.billingInterval,
        organizationId || '',
        webhookData.selectedCredits
      );

      if (result.success && organizationId) {
        dispatch(updateOrganizationCredits({
          organizationId,
          credits: result.creditsAllocated,
          source: 'paddle_purchase',
        }));

        dispatch(makeToast({
          content: `Successfully processed payment and added ${result.creditsAllocated} credits!`,
          appearances: 'success',
        }));
      }

      return result;
    } catch (error) {
      console.error('Failed to process webhook:', error);
      dispatch(makeToast({
        content: 'Failed to process payment. Please contact support.',
        appearances: 'error',
      }));
      throw error;
    }
  }, [organizationId, dispatch]);

  return {
    processWebhook,
  };
};
