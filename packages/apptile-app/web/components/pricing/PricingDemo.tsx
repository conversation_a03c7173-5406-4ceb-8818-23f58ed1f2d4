import React, {useState} from 'react';
import {View, Text, StyleSheet, Pressable} from 'react-native';
import {useDispatch} from 'react-redux';
import Button from '../../components-v2/base/Button';
import {PricingModal} from './PricingModal';
import {CreditDisplay} from './CreditDisplay';
import {makeToast} from '../../actions/toastActions';
import {updateOrganizationCredits} from '../../actions/pricingActions';

/**
 * Demo component to showcase the Paddle integration features
 * This component demonstrates:
 * - Pricing modal functionality
 * - Credit display and management
 * - Simulated payment flows
 * - Toast notifications
 */
export const PricingDemo: React.FC = () => {
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [demoCredits, setDemoCredits] = useState(85);
  const dispatch = useDispatch();

  const simulatePayment = (planName: string, credits: number) => {
    dispatch(makeToast({
      content: `Simulating payment for ${planName} plan...`,
      appearances: 'info',
    }));

    setTimeout(() => {
      setDemoCredits(prev => prev + credits);
      dispatch(makeToast({
        content: `Payment successful! Added ${credits} credits to your account.`,
        appearances: 'success',
      }));
    }, 2000);
  };

  const simulateUsage = () => {
    if (demoCredits > 0) {
      setDemoCredits(prev => Math.max(0, prev - 10));
      dispatch(makeToast({
        content: 'Used 10 credits for AI generation',
        appearances: 'info',
      }));
    } else {
      dispatch(makeToast({
        content: 'No credits remaining! Please upgrade your plan.',
        appearances: 'error',
      }));
    }
  };

  const resetDemo = () => {
    setDemoCredits(85);
    dispatch(makeToast({
      content: 'Demo reset to initial state',
      appearances: 'info',
    }));
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Paddle Integration Demo</Text>
      <Text style={styles.subtitle}>
        This demo showcases the complete Paddle payment integration similar to Lovable's pricing system.
      </Text>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Credit Status</Text>
        <View style={styles.creditContainer}>
          <CreditDisplay 
            onPress={() => setShowPricingModal(true)} 
            showDetails={true}
          />
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Demo Actions</Text>
        <View style={styles.buttonContainer}>
          <Button
            variant="FILLED"
            color="PRIMARY"
            containerStyles={styles.demoButton}
            onPress={() => setShowPricingModal(true)}>
            Open Pricing Modal
          </Button>
          
          <Button
            variant="FILLED"
            color="SECONDARY"
            containerStyles={styles.demoButton}
            onPress={simulateUsage}>
            Use 10 Credits
          </Button>
          
          <Button
            variant="PILL"
            color="DEFAULT"
            containerStyles={styles.demoButton}
            onPress={resetDemo}>
            Reset Demo
          </Button>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Purchase Simulation</Text>
        <View style={styles.buttonContainer}>
          <Button
            variant="FILLED"
            color="SUCCESS"
            containerStyles={styles.purchaseButton}
            onPress={() => simulatePayment('Pro (100 credits)', 100)}>
            Buy Pro Plan
          </Button>
          
          <Button
            variant="FILLED"
            color="SUCCESS"
            containerStyles={styles.purchaseButton}
            onPress={() => simulatePayment('Pro (400 credits)', 400)}>
            Buy Pro+ Plan
          </Button>
          
          <Button
            variant="FILLED"
            color="SUCCESS"
            containerStyles={styles.purchaseButton}
            onPress={() => simulatePayment('Teams', 100)}>
            Buy Teams Plan
          </Button>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Features Demonstrated</Text>
        <View style={styles.featureList}>
          <Text style={styles.featureItem}>✅ Paddle.js integration with client token</Text>
          <Text style={styles.featureItem}>✅ Lovable-style pricing modal</Text>
          <Text style={styles.featureItem}>✅ Credit-based billing system</Text>
          <Text style={styles.featureItem}>✅ Organization credit management</Text>
          <Text style={styles.featureItem}>✅ Real-time credit display</Text>
          <Text style={styles.featureItem}>✅ Webhook simulation</Text>
          <Text style={styles.featureItem}>✅ Toast notifications</Text>
          <Text style={styles.featureItem}>✅ Monthly/Annual billing toggle</Text>
          <Text style={styles.featureItem}>✅ Multiple credit tiers</Text>
          <Text style={styles.featureItem}>✅ Responsive design</Text>
        </View>
      </View>

      <PricingModal
        visible={showPricingModal}
        onClose={() => setShowPricingModal(false)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 24,
    backgroundColor: '#1a1a1a',
    minHeight: '100vh',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  section: {
    marginBottom: 32,
    padding: 20,
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#3a3a3a',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 16,
  },
  creditContainer: {
    alignItems: 'flex-start',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  demoButton: {
    minWidth: 150,
  },
  purchaseButton: {
    minWidth: 140,
  },
  featureList: {
    gap: 8,
  },
  featureItem: {
    fontSize: 14,
    color: '#ffffff',
    lineHeight: 20,
  },
});
