import React, {useState, useEffect} from 'react';
import {useSelector, useDispatch} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import {makeToast} from '../../actions/toastActions';
import {PaddleService, PADDLE_PRODUCTS, PaddlePrice, PaddleCheckoutData} from '../../api/PaddleApi';

interface PricingModalProps {
  visible: boolean;
  onClose: () => void;
}

export const PricingModal: React.FC<PricingModalProps> = ({visible, onClose}) => {
  const [paddleService] = useState(() => PaddleService.getInstance());
  const [prices, setPrices] = useState<{[key: string]: PaddlePrice}>({});
  const [loading, setLoading] = useState(false);
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'annual'>('annual');

  const dispatch = useDispatch();
  const user = useSelector((state: EditorRootState) => state.user.user);
  const orgId = useSelector((state: EditorRootState) => state.platform.orgId);

  // Initialize Paddle when modal opens
  useEffect(() => {
    if (visible) {
      initializePaddle();
    }
  }, [visible]);

  const initializePaddle = async () => {
    setLoading(true);

    try {
      const initialized = await paddleService.initializePaddle(handlePaddleEvent);
      if (initialized) {
        await loadPrices();
      } else {
        dispatch(makeToast({
          content: 'Failed to load payment system',
          appearances: 'error'
        }));
      }
    } catch (error) {
      console.error('Failed to initialize Paddle:', error);
      dispatch(makeToast({
        content: 'Failed to load payment system',
        appearances: 'error'
      }));
    } finally {
      setLoading(false);
    }
  };

  // Load prices from Paddle
  const loadPrices = async () => {
    const pricePromises = Object.entries(PADDLE_PRODUCTS).map(async ([key, productId]) => {
      try {
        const priceData = await paddleService.getProductPrices(productId);
        if (priceData && priceData.length > 0) {
          setPrices(prev => ({
            ...prev,
            [key]: priceData[0]
          }));
        }
      } catch (error) {
        console.error(`Failed to load prices for ${key}:`, error);
      }
    });

    await Promise.all(pricePromises);
  };

  // Handle Paddle events
  const handlePaddleEvent = (data: any) => {
    switch (data.event) {
      case 'Checkout.Complete':
        handleCheckoutSuccess(data);
        break;
      case 'Checkout.Close':
        console.log('Checkout closed');
        break;
      case 'Checkout.Error':
        handleCheckoutError(data);
        break;
    }
  };

  const handleCheckoutSuccess = (data: any) => {
    dispatch(makeToast({
      content: 'Payment successful! Your credits will be added shortly.',
      appearances: 'success'
    }));

    // Here you would typically call your backend to process the webhook
    // and allocate credits to the user's organization
    console.log('Checkout success data:', data);

    onClose();
  };

  const handleCheckoutError = (data: any) => {
    dispatch(makeToast({
      content: 'Payment failed. Please try again.',
      appearances: 'error'
    }));
    console.error('Checkout error:', data);
  };

  const handleUpgrade = (planType: 'pro' | 'teams') => {
    if (!user?.email) {
      dispatch(makeToast({content: 'Please log in to upgrade', appearances: 'error'}));
      return;
    }

    if (!paddleService.isPaddleLoaded()) {
      dispatch(makeToast({content: 'Payment system is loading, please try again', appearances: 'error'}));
      return;
    }

    // Get the appropriate product ID based on plan and billing interval
    const productKey = `${planType}_${billingInterval}` as keyof typeof PADDLE_PRODUCTS;
    const productId = PADDLE_PRODUCTS[productKey];

    if (!productId) {
      dispatch(makeToast({content: 'Product not found', appearances: 'error'}));
      return;
    }

    try {
      // Open Paddle checkout using the service
      paddleService.openCheckout({
        product: productId,
        email: user.email,
        allowQuantity: false,
        customData: {
          organization_id: orgId,
          user_id: user.id?.toString(),
          plan_type: planType,
        },
        successCallback: (data: PaddleCheckoutData) => {
          handleCheckoutSuccess(data);
        },
        closeCallback: () => {
          console.log('Checkout closed by user');
        }
      });
    } catch (error) {
      console.error('Failed to open checkout:', error);
      dispatch(makeToast({content: 'Failed to open checkout', appearances: 'error'}));
    }
  };

  const renderPricingCard = (planType: 'free' | 'pro' | 'teams', planName: string, description: string, features: string[]) => {
    const priceKey = `${planType}_${billingInterval}` as keyof typeof PADDLE_PRODUCTS;
    const priceData = prices[priceKey];

    let displayPrice = '$0';
    if (priceData) {
      const price = parseFloat(priceData.price.gross);
      displayPrice = billingInterval === 'annual' ? `$${Math.round(price / 12)}` : `$${price}`;
    } else if (planType === 'pro') {
      displayPrice = billingInterval === 'monthly' ? '$25' : '$23';
    } else if (planType === 'teams') {
      displayPrice = billingInterval === 'monthly' ? '$30' : '$27';
    }

    return (
      <div key={planType} className={`pricing-card ${planType === 'pro' ? 'popular' : ''}`}>
        {planType === 'pro' && (
          <div className="popular-badge">
            POPULAR
          </div>
        )}

        <div className="card-header">
          <h3 className="plan-name">{planName}</h3>
          <div className="price-container">
            <span className="price">{displayPrice}</span>
            <span className="price-unit">/month</span>
          </div>
          <p className="plan-description">{description}</p>
        </div>

        <div className="features-container">
          {features.map((feature, index) => (
            <div key={index} className="feature-row">
              <span className="check-icon">✓</span>
              <span className="feature-text">{feature}</span>
            </div>
          ))}
        </div>

        <button
          className={`upgrade-button ${planType === 'pro' ? 'primary' : 'secondary'}`}
          onClick={() => planType !== 'free' ? handleUpgrade(planType as 'pro' | 'teams') : null}
          disabled={loading || planType === 'free'}>
          {planType === 'free' ? 'Current Plan' : 'Upgrade'}
        </button>
      </div>
    );
  };

  if (!visible) return null;

  return (
    <div className="pricing-modal-overlay" onClick={onClose}>
      <div className="pricing-modal-container" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2 className="modal-title">Pricing</h2>
          <button className="close-button" onClick={onClose}>
            ×
          </button>
        </div>

        <p className="modal-subtitle">
          Start for free. Upgrade to get the capacity that exactly matches your team's needs.
        </p>

        <div className="billing-toggle">
          <button
            className={`toggle-option ${billingInterval === 'monthly' ? 'active' : ''}`}
            onClick={() => setBillingInterval('monthly')}>
            Monthly
          </button>
          <button
            className={`toggle-option ${billingInterval === 'annual' ? 'active' : ''}`}
            onClick={() => setBillingInterval('annual')}>
            Annual (Save 10%)
          </button>
        </div>

        {loading ? (
          <div className="loading-container">
            <div className="spinner">Loading...</div>
          </div>
        ) : (
          <div className="plans-container">
            {renderPricingCard('free', 'Free', 'For getting started', ['5 daily credits', 'Public projects'])}
            {renderPricingCard('pro', 'Pro', 'For more projects and usage', ['100+ credits / month', 'Private projects', 'Priority support'])}
            {renderPricingCard('teams', 'Teams', 'For collaborating with others', ['Everything in Pro, plus:', 'Centralized billing', 'Team management', 'Includes 20 seats'])}
          </div>
        )}

        <style>{`
          .pricing-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
          }

          .pricing-modal-container {
            background: #1a1a1a;
            border-radius: 16px;
            padding: 24px;
            max-width: 900px;
            width: 95%;
            max-height: 90vh;
            overflow-y: auto;
            color: white;
          }

          .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
          }

          .modal-title {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            margin: 0;
          }

          .close-button {
            background: none;
            border: none;
            color: #666;
            font-size: 24px;
            cursor: pointer;
            padding: 8px;
          }

          .modal-subtitle {
            font-size: 16px;
            color: #999;
            text-align: center;
            margin-bottom: 32px;
            line-height: 24px;
          }

          .billing-toggle {
            display: flex;
            background: #2a2a2a;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 32px;
            align-self: center;
            width: fit-content;
            margin-left: auto;
            margin-right: auto;
          }

          .toggle-option {
            padding: 8px 16px;
            border-radius: 6px;
            background: none;
            border: none;
            color: #999;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
          }

          .toggle-option.active {
            background: #0062FF;
            color: #ffffff;
          }

          .loading-container {
            padding: 40px;
            text-align: center;
          }

          .spinner {
            color: #0062FF;
          }

          .plans-container {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 24px;
          }

          .pricing-card {
            background: #2a2a2a;
            border-radius: 12px;
            padding: 20px;
            width: 260px;
            border: 1px solid #3a3a3a;
            position: relative;
          }

          .pricing-card.popular {
            border-color: #0062FF;
            border-width: 2px;
          }

          .popular-badge {
            position: absolute;
            top: -8px;
            left: 20px;
            right: 20px;
            background: #0062FF;
            padding: 4px 8px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
          }

          .card-header {
            margin-bottom: 20px;
          }

          .plan-name {
            font-size: 20px;
            font-weight: bold;
            color: #ffffff;
            margin: 0 0 6px 0;
          }

          .price-container {
            display: flex;
            align-items: baseline;
            margin-bottom: 6px;
          }

          .price {
            font-size: 36px;
            font-weight: bold;
            color: #ffffff;
          }

          .price-unit {
            font-size: 14px;
            color: #999;
            margin-left: 4px;
          }

          .plan-description {
            font-size: 14px;
            color: #999;
            margin: 0;
          }

          .features-container {
            margin-bottom: 24px;
            min-height: 120px;
          }

          .feature-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
          }

          .check-icon {
            color: #4CAF50;
            margin-right: 8px;
            font-weight: bold;
          }

          .feature-text {
            color: #ffffff;
            font-size: 13px;
            line-height: 18px;
          }

          .upgrade-button {
            width: 100%;
            padding: 12px;
            border-radius: 8px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
          }

          .upgrade-button.primary {
            background: #0062FF;
            color: white;
          }

          .upgrade-button.secondary {
            background: #4A4A4A;
            color: white;
          }

          .upgrade-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        `}</style>
      </div>
    </div>
  );
};
