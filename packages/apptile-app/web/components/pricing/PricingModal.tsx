import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, Pressable, ActivityIndicator} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import Modal from '../../components-v2/base/Modal';
import Button from '../../components-v2/base/Button';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import PaddleApi, {PaddlePlan} from '../../api/PaddleApi';
import {EditorRootState} from '../../store/EditorRootState';
import {makeToast} from '../../actions/toastActions';
import {usePaddleCheckout} from './PaddleCheckout';
import {
  fetchPricingPlans,
  createPaddleCheckout,
  fetchOrganizationCredits,
} from '../../actions/pricingActions';

interface PricingModalProps {
  visible: boolean;
  onClose: () => void;
}

const MOCK_PLANS: PaddlePlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'For getting started',
    price: {monthly: 0, annual: 0},
    credits: 5,
    features: ['5 daily credits', 'Public projects'],
    popular: false,
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'For more projects and usage',
    price: {monthly: 25, annual: 275}, // 10% discount annually
    credits: 100,
    features: ['100 credits / month', '200 credits / month', '400 credits / month', '800 credits / month', '1200 credits / month', '2000 credits / month', '3000 credits / month', '4000 credits / month'],
    popular: true,
  },
  {
    id: 'teams',
    name: 'Teams',
    description: 'For collaborating with others',
    price: {monthly: 30, annual: 324}, // 10% discount annually
    credits: 100,
    features: ['Everything in Pro, plus:', 'Centralised billing', 'Centralised access management', 'Includes 20 seats'],
    popular: false,
  },
];

export const PricingModal: React.FC<PricingModalProps> = ({visible, onClose}) => {
  const [plans, setPlans] = useState<PaddlePlan[]>(MOCK_PLANS);
  const [loading, setLoading] = useState(false);
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'annual'>('annual');
  const [selectedCredits, setSelectedCredits] = useState<{[key: string]: number}>({
    pro: 100,
  });

  const dispatch = useDispatch();
  const user = useSelector((state: EditorRootState) => state.user.user);
  const orgId = useSelector((state: EditorRootState) => state.platform.orgId);
  const pricingState = useSelector((state: EditorRootState) => state.pricing);
  const {openCheckout, isReady: paddleReady} = usePaddleCheckout();

  useEffect(() => {
    if (visible) {
      loadPlans();
    }
  }, [visible]);

  const loadPlans = async () => {
    try {
      setLoading(true);
      // Use Redux action to fetch plans
      dispatch(fetchPricingPlans());

      // For demo purposes, we'll use mock data
      // In production, the plans will be loaded via the saga
      setPlans(MOCK_PLANS);
    } catch (error) {
      console.error('Failed to load plans:', error);
      dispatch(makeToast({content: 'Failed to load pricing plans', appearances: 'error'}));
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (planId: string) => {
    if (!user?.id || !orgId) {
      dispatch(makeToast({content: 'Please log in to upgrade', appearances: 'error'}));
      return;
    }

    if (planId === 'free') {
      dispatch(makeToast({content: 'You are already on the free plan', appearances: 'info'}));
      return;
    }

    try {
      setLoading(true);

      // For demo purposes, we'll simulate the checkout process
      // In production, use the Redux action to create checkout:
      dispatch(createPaddleCheckout({
        planId,
        billingInterval,
        organizationId: orgId,
        userId: user.id.toString(),
      }));

      // Simulate successful upgrade for demo
      setTimeout(() => {
        dispatch(makeToast({
          content: `Successfully upgraded to ${plans.find(p => p.id === planId)?.name} plan!`,
          appearances: 'success'
        }));

        // Update organization credits based on plan
        const selectedPlan = plans.find(p => p.id === planId);
        if (selectedPlan) {
          const creditsToAdd = planId === 'pro' ? selectedCredits.pro : selectedPlan.credits;
          // In production, this would be handled by the webhook after successful payment
          // For demo, we simulate adding credits
          console.log(`Adding ${creditsToAdd} credits to organization ${orgId}`);
        }

        onClose();
      }, 2000);

    } catch (error) {
      console.error('Failed to create checkout:', error);
      dispatch(makeToast({content: 'Failed to start checkout process', appearances: 'error'}));
    } finally {
      setTimeout(() => setLoading(false), 2000);
    }
  };

  const renderPricingCard = (plan: PaddlePlan) => {
    const price = billingInterval === 'monthly' ? plan.price.monthly : plan.price.annual;
    const monthlyPrice = billingInterval === 'annual' ? Math.round(price / 12) : price;
    
    return (
      <View key={plan.id} style={[styles.pricingCard, plan.popular && styles.popularCard]}>
        {plan.popular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularText}>POPULAR</Text>
          </View>
        )}
        
        <View style={styles.cardHeader}>
          <Text style={styles.planName}>{plan.name}</Text>
          <View style={styles.priceContainer}>
            <Text style={styles.price}>${monthlyPrice}</Text>
            <Text style={styles.priceUnit}>/month</Text>
          </View>
          <Text style={styles.planDescription}>{plan.description}</Text>
        </View>

        <View style={styles.featuresContainer}>
          {plan.id === 'pro' ? (
            <View style={styles.creditsSelector}>
              <Text style={styles.creditsLabel}>Credits per month:</Text>
              <View style={styles.creditsOptions}>
                {[100, 200, 400, 800, 1200, 2000, 3000, 4000].map((credits) => (
                  <Pressable
                    key={credits}
                    style={[
                      styles.creditsOption,
                      selectedCredits.pro === credits && styles.selectedCreditsOption
                    ]}
                    onPress={() => setSelectedCredits({...selectedCredits, pro: credits})}>
                    <Text style={[
                      styles.creditsOptionText,
                      selectedCredits.pro === credits && styles.selectedCreditsOptionText
                    ]}>
                      {credits} credits / month
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>
          ) : (
            plan.features.map((feature, index) => (
              <View key={index} style={styles.featureRow}>
                <Icon name="check" size={16} color="#4CAF50" />
                <Text style={styles.featureText}>{feature}</Text>
              </View>
            ))
          )}
        </View>

        <Button
          variant="FILLED"
          color={plan.popular ? "PRIMARY" : "SECONDARY"}
          containerStyles={styles.upgradeButton}
          onPress={() => handleUpgrade(plan.id)}
          loading={loading}
          disabled={loading}>
          {plan.id === 'free' ? 'Get started with' : 'Upgrade'}
        </Button>
      </View>
    );
  };

  const modalContent = (
    <View style={styles.modalContainer}>
      <View style={styles.header}>
        <Text style={styles.title}>Pricing</Text>
        <Pressable onPress={onClose} style={styles.closeButton}>
          <Icon name="close" size={24} color="#666" />
        </Pressable>
      </View>
      
      <Text style={styles.subtitle}>
        Start for free. Upgrade to get the capacity that exactly matches your team's needs.
      </Text>

      <View style={styles.billingToggle}>
        <Pressable
          style={[styles.toggleOption, billingInterval === 'monthly' && styles.activeToggle]}
          onPress={() => setBillingInterval('monthly')}>
          <Text style={[styles.toggleText, billingInterval === 'monthly' && styles.activeToggleText]}>
            Monthly
          </Text>
        </Pressable>
        <Pressable
          style={[styles.toggleOption, billingInterval === 'annual' && styles.activeToggle]}
          onPress={() => setBillingInterval('annual')}>
          <Text style={[styles.toggleText, billingInterval === 'annual' && styles.activeToggleText]}>
            Annual (Save 10%)
          </Text>
        </Pressable>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#0062FF" />
        </View>
      ) : (
        <View style={styles.plansContainer}>
          {plans.map(renderPricingCard)}
        </View>
      )}
    </View>
  );

  return (
    <Modal
      visible={visible}
      onVisibleChange={(visible) => !visible && onClose()}
      content={modalContent}
      modalBackgroundStyles={styles.modalBackground}
    />
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  modalContainer: {
    backgroundColor: '#1a1a1a',
    borderRadius: 16,
    padding: 24,
    maxWidth: 1200,
    width: '90%',
    maxHeight: '90%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    fontFamily: 'General Sans',
  },
  closeButton: {
    padding: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    marginBottom: 32,
    fontFamily: 'General Sans',
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: '#2a2a2a',
    borderRadius: 8,
    padding: 4,
    marginBottom: 32,
    alignSelf: 'center',
  },
  toggleOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  activeToggle: {
    backgroundColor: '#0062FF',
  },
  toggleText: {
    color: '#999',
    fontSize: 14,
    fontWeight: '500',
  },
  activeToggleText: {
    color: '#ffffff',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  plansContainer: {
    flexDirection: 'row',
    gap: 24,
    justifyContent: 'center',
    flexWrap: 'wrap',
  },
  pricingCard: {
    backgroundColor: '#2a2a2a',
    borderRadius: 12,
    padding: 24,
    minWidth: 300,
    maxWidth: 350,
    flex: 1,
    borderWidth: 1,
    borderColor: '#3a3a3a',
    position: 'relative',
  },
  popularCard: {
    borderColor: '#0062FF',
    borderWidth: 2,
  },
  popularBadge: {
    position: 'absolute',
    top: -1,
    left: '50%',
    transform: [{translateX: -50}],
    backgroundColor: '#0062FF',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardHeader: {
    marginBottom: 24,
  },
  planName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  price: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  priceUnit: {
    fontSize: 16,
    color: '#999',
    marginLeft: 4,
  },
  planDescription: {
    fontSize: 16,
    color: '#999',
  },
  featuresContainer: {
    marginBottom: 24,
    minHeight: 200,
  },
  featureRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    color: '#ffffff',
    marginLeft: 8,
    fontSize: 14,
  },
  creditsSelector: {
    marginBottom: 16,
  },
  creditsLabel: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
  },
  creditsOptions: {
    gap: 8,
  },
  creditsOption: {
    backgroundColor: '#3a3a3a',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#4a4a4a',
  },
  selectedCreditsOption: {
    backgroundColor: '#0062FF',
    borderColor: '#0062FF',
  },
  creditsOptionText: {
    color: '#ffffff',
    fontSize: 14,
  },
  selectedCreditsOptionText: {
    color: '#ffffff',
    fontWeight: '500',
  },
  upgradeButton: {
    marginTop: 'auto',
  },
});
