import {DispatchAction} from 'apptile-core';
import {SagaIterator} from '@redux-saga/types';
import {all, call, put, takeLatest} from 'redux-saga/effects';
import {
  FETCH_PRICING_PLANS,
  FETCH_PRICING_PLANS_SUCCESS,
  FETCH_PRICING_PLANS_FAILED,
  CREATE_PADDLE_CHECKOUT,
  CREATE_PADDLE_CHECKOUT_SUCCESS,
  CREATE_PADDLE_CHECKOUT_FAILED,
  FETCH_ORGANIZATION_CREDITS,
  FETCH_<PERSON>GANIZATION_CREDITS_SUCCESS,
  FETCH_<PERSON><PERSON><PERSON><PERSON>ATION_CREDITS_FAILED,
  UPDATE_ORGA<PERSON>ZATION_CREDITS,
  UPDATE_ORGANIZATION_CREDITS_SUCCESS,
  UPDATE_ORGANIZATION_CREDITS_FAILED,
  FetchPricingPlansPayload,
  CreatePaddleCheckoutPayload,
  FetchOrganizationCreditsPayload,
  UpdateOrganizationCreditsPayload,
} from '../actions/pricingActions';
import {makeToast} from '../actions/toastActions';
import <PERSON><PERSON><PERSON><PERSON> from '../api/PaddleApi';

export function* fetchPricingPlans(action: DispatchAction<FetchPricingPlansPayload>): SagaIterator {
  try {
    const response = yield call(PaddleApi.fetchPlans);
    yield put({
      type: FETCH_PRICING_PLANS_SUCCESS,
      payload: response.data,
    });
  } catch (error) {
    yield put({
      type: FETCH_PRICING_PLANS_FAILED,
      payload: error,
    });
    yield put(makeToast({
      content: 'Failed to load pricing plans',
      appearances: 'error',
    }));
  }
}

export function* createPaddleCheckout(action: DispatchAction<CreatePaddleCheckoutPayload>): SagaIterator {
  try {
    const {planId, billingInterval, organizationId, userId} = action.payload;
    const response = yield call(PaddleApi.createCheckout, planId, billingInterval, organizationId, userId);
    
    yield put({
      type: CREATE_PADDLE_CHECKOUT_SUCCESS,
      payload: response.data,
    });

    // Open Paddle checkout in new window
    if (response.data.checkoutUrl) {
      window.open(response.data.checkoutUrl, '_blank');
    }

    yield put(makeToast({
      content: 'Redirecting to checkout...',
      appearances: 'info',
    }));
  } catch (error) {
    yield put({
      type: CREATE_PADDLE_CHECKOUT_FAILED,
      payload: error,
    });
    yield put(makeToast({
      content: 'Failed to create checkout session',
      appearances: 'error',
    }));
  }
}

export function* fetchOrganizationCredits(action: DispatchAction<FetchOrganizationCreditsPayload>): SagaIterator {
  try {
    const {organizationId} = action.payload;
    const response = yield call(PaddleApi.getCreditBalance, organizationId);
    
    yield put({
      type: FETCH_ORGANIZATION_CREDITS_SUCCESS,
      payload: response.data,
    });
  } catch (error) {
    yield put({
      type: FETCH_ORGANIZATION_CREDITS_FAILED,
      payload: error,
    });
    yield put(makeToast({
      content: 'Failed to fetch organization credits',
      appearances: 'error',
    }));
  }
}

export function* updateOrganizationCredits(action: DispatchAction<UpdateOrganizationCreditsPayload>): SagaIterator {
  try {
    const {organizationId, credits, source} = action.payload;
    const response = yield call(PaddleApi.addCredits, organizationId, credits, source);
    
    yield put({
      type: UPDATE_ORGANIZATION_CREDITS_SUCCESS,
      payload: response.data,
    });

    yield put(makeToast({
      content: `Successfully added ${credits} credits to organization`,
      appearances: 'success',
    }));

    // Refresh the credits balance
    yield put({
      type: FETCH_ORGANIZATION_CREDITS,
      payload: {organizationId},
    });
  } catch (error) {
    yield put({
      type: UPDATE_ORGANIZATION_CREDITS_FAILED,
      payload: error,
    });
    yield put(makeToast({
      content: 'Failed to update organization credits',
      appearances: 'error',
    }));
  }
}

export function* pricingSagas(): SagaIterator {
  yield all([
    takeLatest(FETCH_PRICING_PLANS, fetchPricingPlans),
    takeLatest(CREATE_PADDLE_CHECKOUT, createPaddleCheckout),
    takeLatest(FETCH_ORGANIZATION_CREDITS, fetchOrganizationCredits),
    takeLatest(UPDATE_ORGANIZATION_CREDITS, updateOrganizationCredits),
  ]);
}
