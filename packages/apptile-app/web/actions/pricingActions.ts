import {DispatchAction} from 'apptile-core';
import {PaddlePlan} from '../api/PaddleApi';

// Action Types
export const FETCH_PRICING_PLANS = 'FETCH_PRICING_PLANS';
export const FETCH_PRICING_PLANS_SUCCESS = 'FETCH_PRICING_PLANS_SUCCESS';
export const FETCH_PRICING_PLANS_FAILED = 'FETCH_PRICING_PLANS_FAILED';

export const CREATE_PADDLE_CHECKOUT = 'CREATE_PADDLE_CHECKOUT';
export const CREATE_PADDLE_CHECKOUT_SUCCESS = 'CREATE_PADDLE_CHECKOUT_SUCCESS';
export const CREATE_PADDLE_CHECKOUT_FAILED = 'CREATE_PADDLE_CHECKOUT_FAILED';

export const FETCH_ORGANIZATION_CREDITS = 'FETCH_ORGANIZATION_CREDITS';
export const FETCH_ORGANIZATION_CREDITS_SUCCESS = 'FETCH_ORGANIZATION_CREDITS_SUCCESS';
export const FETCH_ORGANIZATION_CREDITS_FAILED = 'FETCH_ORGANIZATION_CREDITS_FAILED';

export const UPDATE_ORGANIZATION_CREDITS = 'UPDATE_ORGANIZATION_CREDITS';
export const UPDATE_ORGANIZATION_CREDITS_SUCCESS = 'UPDATE_ORGANIZATION_CREDITS_SUCCESS';
export const UPDATE_ORGANIZATION_CREDITS_FAILED = 'UPDATE_ORGANIZATION_CREDITS_FAILED';

// Action Interfaces
export interface FetchPricingPlansPayload {}

export interface CreatePaddleCheckoutPayload {
  planId: string;
  billingInterval: 'monthly' | 'annual';
  organizationId: string;
  userId: string;
}

export interface FetchOrganizationCreditsPayload {
  organizationId: string;
}

export interface UpdateOrganizationCreditsPayload {
  organizationId: string;
  credits: number;
  source: string;
}

// Action Creators
export const fetchPricingPlans = (): DispatchAction<FetchPricingPlansPayload> => ({
  type: FETCH_PRICING_PLANS,
  payload: {},
});

export const fetchPricingPlansSuccess = (plans: PaddlePlan[]): DispatchAction<PaddlePlan[]> => ({
  type: FETCH_PRICING_PLANS_SUCCESS,
  payload: plans,
});

export const fetchPricingPlansFailed = (error: any): DispatchAction<any> => ({
  type: FETCH_PRICING_PLANS_FAILED,
  payload: error,
});

export const createPaddleCheckout = (payload: CreatePaddleCheckoutPayload): DispatchAction<CreatePaddleCheckoutPayload> => ({
  type: CREATE_PADDLE_CHECKOUT,
  payload,
});

export const createPaddleCheckoutSuccess = (checkoutData: any): DispatchAction<any> => ({
  type: CREATE_PADDLE_CHECKOUT_SUCCESS,
  payload: checkoutData,
});

export const createPaddleCheckoutFailed = (error: any): DispatchAction<any> => ({
  type: CREATE_PADDLE_CHECKOUT_FAILED,
  payload: error,
});

export const fetchOrganizationCredits = (payload: FetchOrganizationCreditsPayload): DispatchAction<FetchOrganizationCreditsPayload> => ({
  type: FETCH_ORGANIZATION_CREDITS,
  payload,
});

export const fetchOrganizationCreditsSuccess = (credits: {balance: number; used: number; total: number}): DispatchAction<{balance: number; used: number; total: number}> => ({
  type: FETCH_ORGANIZATION_CREDITS_SUCCESS,
  payload: credits,
});

export const fetchOrganizationCreditsFailed = (error: any): DispatchAction<any> => ({
  type: FETCH_ORGANIZATION_CREDITS_FAILED,
  payload: error,
});

export const updateOrganizationCredits = (payload: UpdateOrganizationCreditsPayload): DispatchAction<UpdateOrganizationCreditsPayload> => ({
  type: UPDATE_ORGANIZATION_CREDITS,
  payload,
});

export const updateOrganizationCreditsSuccess = (result: any): DispatchAction<any> => ({
  type: UPDATE_ORGANIZATION_CREDITS_SUCCESS,
  payload: result,
});

export const updateOrganizationCreditsFailed = (error: any): DispatchAction<any> => ({
  type: UPDATE_ORGANIZATION_CREDITS_FAILED,
  payload: error,
});
