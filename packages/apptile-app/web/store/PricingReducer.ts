import {createReducer} from 'apptile-core';
import {
  FETCH_PRICING_PLANS,
  FETCH_PRICING_PLANS_SUCCESS,
  FETCH_PRICING_PLANS_FAILED,
  CREATE_PADDLE_CHECKOUT,
  CREATE_PADDLE_CHECKOUT_SUCCESS,
  CREATE_PADDLE_CHECKOUT_FAILED,
  FETCH_ORGANIZATION_CREDITS,
  FETCH_ORGANIZATION_CREDITS_SUCCESS,
  FETCH_ORGANI<PERSON>ATION_CREDITS_FAILED,
  UPDATE_ORGANIZATION_CREDITS,
  UPDATE_ORGANIZATION_CREDITS_SUCCESS,
  UPDATE_ORGANIZATION_CREDITS_FAILED,
} from '../actions/pricingActions';
import {PaddlePlan} from '../api/PaddleApi';

export interface PricingState {
  plans: PaddlePlan[];
  plansLoading: boolean;
  plansError: string | null;
  
  checkoutLoading: boolean;
  checkoutError: string | null;
  checkoutData: any | null;
  
  organizationCredits: {
    balance: number;
    used: number;
    total: number;
  } | null;
  creditsLoading: boolean;
  creditsError: string | null;
  
  updateCreditsLoading: boolean;
  updateCreditsError: string | null;
}

const initialState: PricingState = {
  plans: [],
  plansLoading: false,
  plansError: null,
  
  checkoutLoading: false,
  checkoutError: null,
  checkoutData: null,
  
  organizationCredits: null,
  creditsLoading: false,
  creditsError: null,
  
  updateCreditsLoading: false,
  updateCreditsError: null,
};

export const pricingReducer = createReducer(initialState, {
  [FETCH_PRICING_PLANS]: (state) => ({
    ...state,
    plansLoading: true,
    plansError: null,
  }),
  
  [FETCH_PRICING_PLANS_SUCCESS]: (state, action) => ({
    ...state,
    plansLoading: false,
    plans: action.payload,
    plansError: null,
  }),
  
  [FETCH_PRICING_PLANS_FAILED]: (state, action) => ({
    ...state,
    plansLoading: false,
    plansError: action.payload?.message || 'Failed to fetch pricing plans',
  }),
  
  [CREATE_PADDLE_CHECKOUT]: (state) => ({
    ...state,
    checkoutLoading: true,
    checkoutError: null,
    checkoutData: null,
  }),
  
  [CREATE_PADDLE_CHECKOUT_SUCCESS]: (state, action) => ({
    ...state,
    checkoutLoading: false,
    checkoutData: action.payload,
    checkoutError: null,
  }),
  
  [CREATE_PADDLE_CHECKOUT_FAILED]: (state, action) => ({
    ...state,
    checkoutLoading: false,
    checkoutError: action.payload?.message || 'Failed to create checkout',
  }),
  
  [FETCH_ORGANIZATION_CREDITS]: (state) => ({
    ...state,
    creditsLoading: true,
    creditsError: null,
  }),
  
  [FETCH_ORGANIZATION_CREDITS_SUCCESS]: (state, action) => ({
    ...state,
    creditsLoading: false,
    organizationCredits: action.payload,
    creditsError: null,
  }),
  
  [FETCH_ORGANIZATION_CREDITS_FAILED]: (state, action) => ({
    ...state,
    creditsLoading: false,
    creditsError: action.payload?.message || 'Failed to fetch organization credits',
  }),
  
  [UPDATE_ORGANIZATION_CREDITS]: (state) => ({
    ...state,
    updateCreditsLoading: true,
    updateCreditsError: null,
  }),
  
  [UPDATE_ORGANIZATION_CREDITS_SUCCESS]: (state, action) => ({
    ...state,
    updateCreditsLoading: false,
    updateCreditsError: null,
    // Optionally update the credits balance if returned in the response
    organizationCredits: action.payload.credits ? action.payload.credits : state.organizationCredits,
  }),
  
  [UPDATE_ORGANIZATION_CREDITS_FAILED]: (state, action) => ({
    ...state,
    updateCreditsLoading: false,
    updateCreditsError: action.payload?.message || 'Failed to update organization credits',
  }),
});
