import {AxiosPromise} from 'axios';
import {Api} from './Api';

export interface PaddlePlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    annual: number;
  };
  credits: number;
  features: string[];
  popular?: boolean;
}

export interface PaddleCheckoutData {
  checkoutUrl: string;
  checkoutId: string;
}

export interface PaddleWebhookEvent {
  eventType: string;
  data: any;
}

export default class PaddleApi {
  static baseURL = '/api/paddle';
  static clientToken = 'test_f0ff365cf3b22228fc6dde90827';
  
  static getApiUrl() {
    return Api.API_SERVER + PaddleApi.baseURL;
  }

  // Fetch available pricing plans
  static fetchPlans(): AxiosPromise<PaddlePlan[]> {
    return Api.get(PaddleApi.getApiUrl() + '/plans');
  }

  // Create checkout session for a plan
  static createCheckout(
    planId: string,
    billingInterval: 'monthly' | 'annual',
    organizationId: string,
    userId: string
  ): AxiosPromise<PaddleCheckoutData> {
    return Api.post(PaddleApi.getApiUrl() + '/checkout', {
      planId,
      billingInterval,
      organizationId,
      userId,
      clientToken: PaddleApi.clientToken,
    });
  }

  // Handle webhook events from Paddle
  static handleWebhook(webhookData: PaddleWebhookEvent): AxiosPromise<any> {
    return Api.post(PaddleApi.getApiUrl() + '/webhook', webhookData);
  }

  // Get organization's current subscription
  static getSubscription(organizationId: string): AxiosPromise<any> {
    return Api.get(PaddleApi.getApiUrl() + `/subscription/${organizationId}`);
  }

  // Cancel subscription
  static cancelSubscription(subscriptionId: string): AxiosPromise<any> {
    return Api.post(PaddleApi.getApiUrl() + `/subscription/${subscriptionId}/cancel`);
  }

  // Get organization's credit balance
  static getCreditBalance(organizationId: string): AxiosPromise<{balance: number; used: number; total: number}> {
    return Api.get(PaddleApi.getApiUrl() + `/credits/${organizationId}`);
  }

  // Add credits to organization (for admin use)
  static addCredits(organizationId: string, credits: number, source: string): AxiosPromise<any> {
    return Api.post(PaddleApi.getApiUrl() + `/credits/${organizationId}/add`, {
      credits,
      source,
    });
  }
}
