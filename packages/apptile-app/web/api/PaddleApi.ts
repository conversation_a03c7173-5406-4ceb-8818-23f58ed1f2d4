// Paddle.js integration utilities
export const PADDLE_CLIENT_TOKEN = 'test_f0ff365cf3b22228fc6dde90827';

// Your Paddle product IDs - replace these with your actual product IDs from Paddle Dashboard
export const PADDLE_PRODUCTS = {
  pro_monthly: 'pro_monthly_product_id',
  pro_annual: 'pro_annual_product_id',
  teams_monthly: 'teams_monthly_product_id',
  teams_annual: 'teams_annual_product_id',
};

// Paddle.js types
export interface PaddlePrice {
  price: {
    net: string;
    gross: string;
    tax: string;
  };
  product: {
    id: string;
    name: string;
  };
  list_price: {
    net: string;
    gross: string;
    tax: string;
  };
  currency: string;
  recurring: {
    interval: string;
    frequency: number;
  };
}

export interface PaddleCheckoutData {
  checkout: {
    id: string;
    custom_data?: {
      organization_id: string;
      user_id: string;
      plan_type: string;
    };
  };
  order: {
    total: string;
    currency: string;
  };
}

// Utility class for Paddle integration
export class PaddleService {
  private static instance: PaddleService;
  private paddleLoaded = false;

  static getInstance(): PaddleService {
    if (!PaddleService.instance) {
      PaddleService.instance = new PaddleService();
    }
    return PaddleService.instance;
  }

  // Initialize Paddle.js
  async initializePaddle(eventCallback?: (data: any) => void): Promise<boolean> {
    return new Promise((resolve) => {
      if (window.Paddle) {
        if (!this.paddleLoaded) {
          window.Paddle.Setup({
            vendor: PADDLE_CLIENT_TOKEN,
            eventCallback: eventCallback || this.defaultEventCallback,
          });
          this.paddleLoaded = true;
        }
        resolve(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://cdn.paddle.com/paddle/paddle.js';
      script.async = true;
      script.onload = () => {
        if (window.Paddle) {
          window.Paddle.Setup({
            vendor: PADDLE_CLIENT_TOKEN,
            eventCallback: eventCallback || this.defaultEventCallback,
          });
          this.paddleLoaded = true;
          resolve(true);
        } else {
          resolve(false);
        }
      };
      script.onerror = () => resolve(false);
      document.head.appendChild(script);
    });
  }

  // Default event callback
  private defaultEventCallback = (data: any) => {
    console.log('Paddle event:', data);
  };

  // Get product prices
  async getProductPrices(productId: string): Promise<PaddlePrice[]> {
    return new Promise((resolve, reject) => {
      if (!window.Paddle) {
        reject(new Error('Paddle not loaded'));
        return;
      }

      window.Paddle.Product.Prices(productId, (prices: PaddlePrice[]) => {
        resolve(prices);
      });
    });
  }

  // Open checkout
  openCheckout(config: {
    product?: string;
    prices?: string[];
    email?: string;
    country?: string;
    postcode?: string;
    allowQuantity?: boolean;
    quantity?: number;
    customData?: any;
    successCallback?: (data: PaddleCheckoutData) => void;
    closeCallback?: () => void;
  }) {
    if (!window.Paddle) {
      throw new Error('Paddle not loaded');
    }

    window.Paddle.Checkout.open({
      ...config,
      successCallback: config.successCallback,
      closeCallback: config.closeCallback,
    });
  }

  // Check if Paddle is loaded
  isPaddleLoaded(): boolean {
    return this.paddleLoaded && !!window.Paddle;
  }
}

// Global Paddle types
declare global {
  interface Window {
    Paddle: {
      Setup: (config: {vendor: string; eventCallback?: (data: any) => void}) => void;
      Product: {
        Prices: (productId: string, callback: (prices: PaddlePrice[]) => void) => void;
      };
      Checkout: {
        open: (config: {
          product?: string;
          prices?: string[];
          email?: string;
          country?: string;
          postcode?: string;
          allowQuantity?: boolean;
          quantity?: number;
          customData?: any;
          successCallback?: (data: PaddleCheckoutData) => void;
          closeCallback?: () => void;
        }) => void;
      };
    };
  }
}
