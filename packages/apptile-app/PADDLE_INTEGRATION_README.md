# Paddle.js Integration Implementation

This document outlines the complete Paddle.js payment integration implementation for the Apptile platform, using Paddle's native JavaScript library instead of custom API calls.

## Overview

The implementation uses **Paddle.js** directly to:
- **Load Real Pricing Data**: Fetch actual product prices from Paddle
- **Handle Secure Checkout**: Use Paddle's hosted checkout for PCI compliance
- **Process Webhooks**: Handle payment completion events automatically
- **Manage Subscriptions**: Track and manage user subscriptions

## Key Features

✅ **Native Paddle.js Integration** - Uses official Paddle JavaScript library
✅ **Real-time Price Loading** - Fetches actual prices from your Paddle products
✅ **Secure Hosted Checkout** - PCI-compliant payment processing
✅ **Webhook Event Handling** - Automatic payment completion processing
✅ **Responsive UI** - Lovable-style pricing modal with proper styling
✅ **Credit Allocation** - Automatic credit assignment after successful payments

## Files Created/Modified

### 1. Core Paddle Integration
- `web/api/PaddleApi.ts` - Paddle.js service class and utilities
- `web/components/pricing/PricingModal.tsx` - Main pricing modal using Paddle.js

### 2. UI Components
- `web/components/pricing/CreditDisplay.tsx` - Credit balance display widget
- `web/views/prompt-to-app/dashboard/TopBar.tsx` - Added pricing button and credit display

### 3. State Management (Optional)
- `web/actions/pricingActions.ts` - Redux actions for pricing operations
- `web/store/PricingReducer.ts` - Redux reducer for pricing state
- `web/sagas/PricingSaga.ts` - Async operations handling

## Setup Instructions

### 1. Configure Paddle Products

First, create your products in the Paddle Dashboard:

1. **Log into Paddle Dashboard** → Products → Create Product
2. **Create these products:**
   - `Pro Monthly` - $25/month recurring
   - `Pro Annual` - $275/year recurring
   - `Teams Monthly` - $30/month recurring
   - `Teams Annual` - $324/year recurring

3. **Update Product IDs** in `web/api/PaddleApi.ts`:
```typescript
export const PADDLE_PRODUCTS = {
  pro_monthly: 'your_pro_monthly_product_id',
  pro_annual: 'your_pro_annual_product_id',
  teams_monthly: 'your_teams_monthly_product_id',
  teams_annual: 'your_teams_annual_product_id',
};
```

### 2. Configure Client Token

Update your client-side token in `web/api/PaddleApi.ts`:
```typescript
export const PADDLE_CLIENT_TOKEN = 'your_paddle_client_token';
```

### 3. How It Works

#### Paddle.js Integration Flow
1. **Modal Opens** → Paddle.js script loads automatically
2. **Price Loading** → Real prices fetched from Paddle API
3. **User Clicks Upgrade** → Paddle hosted checkout opens
4. **Payment Processing** → Secure payment via Paddle
5. **Webhook Events** → Your backend receives payment confirmation
6. **Credit Allocation** → Credits added to user's organization

#### Key Components

**PaddleService Class** (`web/api/PaddleApi.ts`)
- Singleton service for Paddle.js integration
- Handles script loading and initialization
- Provides methods for price fetching and checkout

**PricingModal Component** (`web/components/pricing/PricingModal.tsx`)
- Responsive pricing interface
- Real-time price loading from Paddle
- Integrated checkout flow

**Credit Display Widget** (`web/components/pricing/CreditDisplay.tsx`)
- Shows current credit balance
- Visual progress indicators
- Clickable to open pricing modal

## Backend Webhook Integration

### Webhook Endpoint Setup

Create a webhook endpoint in your backend to handle Paddle events:

```typescript
// Example webhook handler (Node.js/Express)
app.post('/api/paddle/webhook', (req, res) => {
  const event = req.body;

  switch (event.alert_name) {
    case 'subscription_created':
    case 'subscription_payment_succeeded':
      // Allocate credits to organization
      allocateCredits(event.custom_data.organization_id, event.custom_data.plan_type);
      break;
    case 'subscription_cancelled':
      // Handle cancellation
      handleCancellation(event.custom_data.organization_id);
      break;
  }

  res.status(200).send('OK');
});
```

### Credit Allocation Logic

```typescript
function allocateCredits(organizationId: string, planType: string) {
  const creditMap = {
    'pro': 100,
    'teams': 100,
  };

  const credits = creditMap[planType] || 0;

  // Add credits to organization
  updateOrganizationCredits(organizationId, credits);
}
```

## Usage Examples

### Opening the Pricing Modal

```typescript
// From any component
const [showPricing, setShowPricing] = useState(false);

<button onClick={() => setShowPricing(true)}>
  Upgrade Plan
</button>

<PricingModal
  visible={showPricing}
  onClose={() => setShowPricing(false)}
/>
```

### Using the Credit Display

```typescript
// Shows current credits with click-to-upgrade
<CreditDisplay onPress={() => setShowPricing(true)} />
```

## Testing the Integration

### 1. Test Mode Setup
- Use Paddle's sandbox environment for testing
- Use test product IDs and client tokens
- Test with Paddle's test card numbers

### 2. Webhook Testing
- Use Paddle's webhook simulator
- Test different event types (payment success, failure, cancellation)
- Verify credit allocation works correctly

### 3. UI Testing
- Test modal responsiveness on different screen sizes
- Verify price loading from Paddle
- Test checkout flow end-to-end

## Production Checklist

- [ ] Replace test product IDs with production IDs
- [ ] Update client token to production token
- [ ] Configure production webhook endpoints
- [ ] Implement webhook signature verification
- [ ] Set up proper error monitoring
- [ ] Test with real payment methods
- [ ] Configure proper SSL certificates
- [ ] Set up analytics tracking

## Advantages of This Approach

✅ **No Custom Backend Required** - Paddle.js handles most payment logic
✅ **PCI Compliance** - Paddle handles all sensitive payment data
✅ **Real-time Pricing** - Always shows current prices from Paddle
✅ **Automatic Updates** - Price changes in Paddle reflect immediately
✅ **Reduced Complexity** - Less custom code to maintain
✅ **Better Security** - Fewer attack vectors than custom payment handling

## Support Resources

- **Paddle.js Documentation**: https://developer.paddle.com/paddle-js
- **Webhook Guide**: https://developer.paddle.com/webhooks
- **Testing Guide**: https://developer.paddle.com/getting-started/sandbox
- **Product Setup**: https://developer.paddle.com/getting-started/products-prices

This implementation leverages Paddle.js's full capabilities for a robust, secure, and maintainable payment integration.
