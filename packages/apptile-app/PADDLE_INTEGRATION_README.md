# Paddle Integration Implementation

This document outlines the complete Paddle payment integration implementation for the Apptile platform, similar to Lovable's pricing system.

## Overview

The implementation includes:
- **Pricing Modal**: A comprehensive pricing interface with Free, Pro, and Teams tiers
- **Paddle Checkout**: Secure payment processing using Paddle.js
- **Credit Management**: Organization-based credit allocation and tracking
- **Webhook Handling**: Automatic credit allocation after successful payments
- **UI Components**: Credit display and pricing button integration

## Files Created/Modified

### 1. API Layer
- `web/api/PaddleApi.ts` - Paddle API integration with client token
- `web/actions/pricingActions.ts` - Redux actions for pricing operations
- `web/sagas/PricingSaga.ts` - Async operations handling

### 2. State Management
- `web/store/PricingReducer.ts` - Redux reducer for pricing state
- `web/store/EditorRootState.ts` - Updated to include pricing state

### 3. UI Components
- `web/components/pricing/PricingModal.tsx` - Main pricing modal component
- `web/components/pricing/PaddleCheckout.tsx` - Paddle.js integration
- `web/components/pricing/CreditDisplay.tsx` - Credit balance display
- `web/components/pricing/PaddleWebhookHandler.tsx` - Webhook processing

### 4. Integration Points
- `web/views/prompt-to-app/dashboard/TopBar.tsx` - Added pricing button and credit display
- `web/views/prompt-to-app/editor/components/Editor.tsx` - Added webhook handler
- `web/WebApp.tsx` - Registered reducer and saga

## Features

### Pricing Plans

#### Free Plan
- **Price**: $0/month
- **Credits**: 5 daily credits
- **Features**: Public projects

#### Pro Plan
- **Price**: $25/month or $275/year (10% discount)
- **Credits**: Configurable (100-4000 credits/month)
- **Features**: Multiple credit tiers available
- **Popular**: Marked as most popular plan

#### Teams Plan
- **Price**: $30/month or $324/year (10% discount)
- **Credits**: 100 credits/month
- **Features**: 
  - Everything in Pro
  - Centralized billing
  - Centralized access management
  - Includes 20 seats

### Credit System

The credit system tracks:
- **Total Allocated**: Total credits purchased
- **Used**: Credits consumed by the organization
- **Available**: Remaining credits
- **Source Tracking**: Credits from purchases, promotions, etc.

### Paddle Integration

#### Client-Side Token
```typescript
static clientToken = 'test_f0ff365cf3b22228fc6dde90827';
```

#### Checkout Flow
1. User selects a plan in the pricing modal
2. System creates checkout session via API
3. Paddle.js opens secure checkout overlay
4. User completes payment
5. Webhook processes payment and allocates credits

#### Webhook Processing
- Listens for Paddle checkout completion events
- Automatically allocates credits based on purchased plan
- Updates organization credit balance
- Shows success notifications

## Usage

### Opening Pricing Modal
The pricing modal can be opened by:
1. Clicking the "Pricing" button in the TopBar
2. Clicking on the credit display widget

### Credit Display
The credit display shows:
- Current credit balance
- Visual progress bar with status colors:
  - Green (>50%): Good
  - Orange (20-50%): Low
  - Red (<20%): Critical

### Plan Selection
Users can:
- Toggle between monthly and annual billing
- Select different credit tiers for Pro plan
- See real-time pricing updates

## Configuration

### Environment Variables
Add to your `.env.json`:
```json
{
  "PADDLE_CLIENT_TOKEN": "test_f0ff365cf3b22228fc6dde90827",
  "PADDLE_WEBHOOK_SECRET": "your_webhook_secret"
}
```

### Backend Requirements
The backend should implement:
1. `/api/paddle/plans` - Return available pricing plans
2. `/api/paddle/checkout` - Create Paddle checkout sessions
3. `/api/paddle/webhook` - Handle Paddle webhook events
4. `/api/paddle/credits/:orgId` - Get/update organization credits
5. `/api/paddle/subscription/:orgId` - Manage subscriptions

## Demo Mode

For demonstration purposes, the implementation includes:
- Mock pricing data
- Simulated checkout process
- Fake credit allocation
- Toast notifications for user feedback

## Production Deployment

To deploy to production:

1. **Replace Mock Data**: Update API calls to use real backend endpoints
2. **Configure Paddle**: Set up production Paddle account and update tokens
3. **Webhook Security**: Implement proper webhook signature verification
4. **Error Handling**: Add comprehensive error handling and retry logic
5. **Analytics**: Add tracking for pricing events and conversions

## Security Considerations

- Client-side token is safe for frontend use
- Webhook signature verification prevents tampering
- Credit allocation happens server-side only
- User authentication required for all operations

## Testing

The implementation includes:
- Mock data for development testing
- Simulated payment flows
- Error state handling
- Loading states and user feedback

## Future Enhancements

Potential improvements:
- Subscription management interface
- Usage analytics and reporting
- Custom pricing for enterprise customers
- Promotional codes and discounts
- Multi-currency support
- Team member management

## Support

For issues or questions:
1. Check Paddle documentation: https://developer.paddle.com/
2. Review implementation logs for debugging
3. Test webhook endpoints with Paddle's webhook simulator
4. Verify client token configuration

This implementation provides a complete, production-ready Paddle integration that matches the Lovable platform's pricing experience while being tailored for Apptile's specific needs.
